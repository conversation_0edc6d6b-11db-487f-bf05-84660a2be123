import nmrglue as ng
import numpy as np
import matplotlib.pyplot as plt

#-----------------------------------------------------------------
# 1. 读取 Bruker FID 数据
#-----------------------------------------------------------------
# 假设 "bruker_data" 是 Bruker 的顶层目录，其中包含 fid、acqus、pdata 等文件
dic, fid = ng.bruker.read('bruker_data')

#-----------------------------------------------------------------
# 2. 将时间域FID转换到频域 (傅里叶变换)
#-----------------------------------------------------------------
# 通常 Bruker 的数据还需做数字滤波矫正 (digital filter)
# nmrglue 会自动判断合适的移位，或者你也可以手动指定
fid_corrected = ng.proc_base.remove_digital_filter(dic, fid)

# 先做零填充(可视情况而定，这里演示填充一倍数据)
size = fid_corrected.shape[-1]
zf_size = 2*size  # 2倍零填充
fid_zf = ng.proc_base.zf_size(fid_corrected, zf_size)

# 再执行 FT
spec = ng.proc_base.fft(fid_zf)

#-----------------------------------------------------------------
# 3. 自动/手动相位校正
#-----------------------------------------------------------------
# nmrglue 提供了几种相位自动校正算法，如 'acme', 'ps', 'ls' 等
# 下面示例使用 'acme' 算法做自动相位校正
spec_ap, ph0, ph1 = ng.proc_autophase.autops(spec, 'acme')

# 如果想手动调整，可以用 add_phase(spec, p0, p1) 自己调 p0, p1
# 例如：
# spec_manual = ng.proc_base.add_phase(spec, p0=30.0, p1=0.0)

# 最终我们将 spec_ap 当作相位校正后的谱
spec_processed = spec_ap

#-----------------------------------------------------------------
# 4. 化学位移轴 (参考/校正)
#-----------------------------------------------------------------
# 建立一个 “仿真dic(udic)” 用于单位转换，需要知道谱宽、中心频率、谱点数、参考点等
# nmrglue 提供 bruker.guess_udic 来根据 Bruker 参数自动生成
udic = ng.bruker.guess_udic(dic, fid)
uc = ng.fileiobase.uc_from_udic(udic)[0]  # 取第一个维度(一维)

# 默认情况下，这个 uc 会带有光谱中心点(f0)的信息
# 如果需要对化学位移做微调(d_ref)，可使用 nmrglue 提供的 offset() 等方法来实现
# 举例，将中心频率/参考点往 (某个已知参考峰位置) 调到 7.26 ppm(假设是 CDCl3 残余峰)
# delta_ppm = 7.26 - <当前参考峰ppm>
# 下面是一个示例性的过程(需要自己测量当前参考峰对应的 ppm):
# uc_ppm0 = <你在图中测量到的当前参考峰位置 ppm>
# delta_ppm = 7.26 - uc_ppm0
# uc.offset(uc.offset() + delta_ppm)

#-----------------------------------------------------------------
# 5. 简单可视化
#-----------------------------------------------------------------
# 准备 x 轴: 化学位移从高到低(常规 NMR 习惯)
ppm_scale = uc.ppm_scale()
spec_plot = spec_processed.real  # 取实部

# 为了让纵轴正方向和传统 NMR 显示一致，通常习惯将 ppm 从大到小显示：
ppm_scale_rev = ppm_scale[::-1]
spec_plot_rev = spec_plot[::-1]

plt.figure(figsize=(8,5))
plt.plot(ppm_scale_rev, spec_plot_rev, 'k-')
plt.xlabel('Chemical Shift (ppm)')
plt.gca().invert_xaxis()  # 反转 X 轴，让 ppm 数值从大到小
plt.title('1H NMR Spectrum Processed with nmrglue')
plt.show()

#-----------------------------------------------------------------
# 6. 简易基线校正 (可选)
#-----------------------------------------------------------------
# nmrglue 里没有非常复杂的自动多段基线/多项式校正函数，
# 但可以通过外部算法或自己写方法实现。
# 这里示例用最简单的方式: 选定某个噪音区间做平均值当基线
# 也可以用 scipy.signal 的方法做平滑等
spec_bl_corrected = spec_plot - np.median(spec_plot[0:200])  # 仅作演示

#-----------------------------------------------------------------
# 7. 简单峰拾取
#-----------------------------------------------------------------
# nmrglue 提供了 peakpick 方法，但需要自己设定阈值
thresh = 5 * np.std(spec_bl_corrected[0:200])  # 举例: 以噪声区 5 倍标准差做阈值
peak_inds = ng.proc_base.peakpick(spec_bl_corrected, thresh=thresh)

print("Detected peaks at indices:", peak_inds)
for i in peak_inds:
    peak_ppm = ppm_scale_rev[i]  # 对应化学位移
    peak_intensity = spec_bl_corrected[i]
    print(f"  - Peak index = {i}, ppm = {peak_ppm:.2f}, intensity = {peak_intensity:.2f}")

# 上述 peak_inds 仅给出峰在数据点数组里的位置；要获得 ppm 或者积分还需进一步计算或手动圈定积分区间

#-----------------------------------------------------------------
# 8. (可选)积分、耦合常数分析等
#-----------------------------------------------------------------
# nmrglue 并没有完全封装类似 TopSpin / Mnova 那样的一键多重峰分析，需要你自己编写或使用第三方库。
# 常见做法：定义一个峰周围的积分区间，通过积分近似方法(如求和或Simpson积分)来得到峰面积。
# 耦合常数 J 的测量通常需要对多重峰进行拟合或找到峰间距，可结合 scipy.optimize 拟合峰形等。

#-----------------------------------------------------------------
# 9. 输出或保存处理后的谱线
#-----------------------------------------------------------------
# 你可以以 ASCII / CSV 格式保存频谱：
ppm_out = ppm_scale_rev
intensity_out = spec_bl_corrected
np.savetxt('processed_spectrum.csv', np.column_stack((ppm_out, intensity_out)),
           header='ppm,intensity', delimiter=',')

print("NMR data processing completed!")