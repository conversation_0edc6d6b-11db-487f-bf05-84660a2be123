<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>SMILES化学信息查询</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
</head>
<body class="bg-light">
<div class="container mt-5">
    <h2 class="mb-4">SMILES 化学结构一键查询</h2>
    <form id="smilesForm" class="mb-4">
        <div class="mb-3">
            <label for="smiles" class="form-label">请输入 SMILES：</label>
            <input type="text" class="form-control" id="smiles" name="smiles" placeholder="如 COO">
        </div>
        <button type="submit" class="btn btn-primary">查询</button>
    </form>
    <div id="result" style="display: none;">
        <h4>查询结果</h4>
        <img id="molImg" class="mb-3" src="" alt="结构式" style="max-width:300px;border:1px solid #aaa;">
        <ul class="list-group">
            <li class="list-group-item"><b>SMILES：</b> <span id="r_smiles"></span></li>
            <li class="list-group-item"><b>分子式：</b> <span id="r_formula"></span></li>
            <li class="list-group-item"><b>分子量：</b> <span id="r_molweight"></span></li>
            <li class="list-group-item"><b>摩尔质量：</b> <span id="r_exactmass"></span></li>
            <li class="list-group-item"><b>名称：</b> <span id="r_name"></span></li>
        </ul>
    </div>
    <div id="error" class="alert alert-danger mt-3" style="display:none;"></div>
</div>
<script>
document.getElementById('smilesForm').onsubmit = function(e) {
    e.preventDefault();
    document.getElementById('result').style.display = 'none';
    document.getElementById('error').style.display = 'none';
    const smiles = document.getElementById('smiles').value.trim();
    fetch('/analyze', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({smiles: smiles})
    })
    .then(response => response.json().then(data => ({status: response.status, body: data})))
    .then(res => {
        if (res.status === 200) {
            document.getElementById('molImg').src = 'data:image/png;base64,' + res.body.img_base64;
            document.getElementById('r_smiles').textContent = res.body.smiles;
            document.getElementById('r_formula').textContent = res.body.formula;
            document.getElementById('r_molweight').textContent = res.body.mol_weight;
            document.getElementById('r_exactmass').textContent = res.body.exact_mass;
            document.getElementById('r_name').textContent = res.body.name;
            document.getElementById('result').style.display = '';
        } else {
            document.getElementById('error').textContent = res.body.error;
            document.getElementById('error').style.display = '';
        }
    })
    .catch(() => {
        document.getElementById('error').textContent = "查询失败，请检查网络或服务器。";
        document.getElementById('error').style.display = '';
    });
};
</script>
</body>
</html>
