from flask import Flask, request, jsonify, render_template
from rdkit import Chem
from rdkit.Chem import Descriptors, Draw
from rdkit.Chem.rdMolDescriptors import CalcMolFormula
from pubchempy import get_compounds
import io
import base64
import requests

app = Flask(__name__)



def get_iupac_from_pubchem(smiles):
    url = f"https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/smiles/{smiles}/property/IUPACName/JSON"
    response = requests.get(url, timeout=10, verify=False)  # 不建议生产环境长用 verify=False
    if response.ok:
        try:
            data = response.json()
            name = data['PropertyTable']['Properties'][0].get('IUPACName', '名称不可用')
            return name
        except Exception as e:
            print("解析JSON失败：", e)
            return "解析失败"
    else:
        return "名称不可用"


@app.route('/')
def index():
    return render_template('index.html')

@app.route('/analyze', methods=['POST'])
def analyze():
    smiles = request.json.get('smiles', '')
    mol = Chem.MolFromSmiles(smiles)
    print(smiles, mol)
    if mol is None:
        return jsonify({'error': '无效的 SMILES'}), 400

    # RDKit属性
    formula = CalcMolFormula(mol)
    mol_weight = Descriptors.MolWt(mol)
    exact_mass = Descriptors.ExactMolWt(mol)

    # PubChemPy名称
    name = get_iupac_from_pubchem(smiles)

    # 分子结构式图片生成为Base64
    img_io = io.BytesIO()
    img = Draw.MolToImage(mol, size=(300, 300))
    img.save(img_io, 'PNG')
    img_base64 = base64.b64encode(img_io.getvalue()).decode()

    return jsonify({
        'smiles': smiles,
        'name': name,
        'formula': formula,
        'mol_weight': round(mol_weight, 4),
        'exact_mass': round(exact_mass, 4),
        'img_base64': img_base64
    })

if __name__ == '__main__':
    app.run(debug=True)
