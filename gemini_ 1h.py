import requests
import json

db = """
1H NMR: 11.9 s 1H | 7.28-7.19 m 4H | 3.71 td 1H J = 5.2 |2.52-2.41 m 2H |1.89-1.77 1H m |1.53 d 3H J = 7.2 |0.89 d 6H J = 6.6
1H NMR: 4.05 d 2H J = 6.6 |2.05 s 3H | 1.85-1.76 m 1H | 1.01 d 6H J = 6.6

"""

prompt = '''
角色 (Role):
你是一位世界级的有机化学与波谱学专家。你的任务是基于提供的多维度数据，执行严谨的、逐步的分子结构解析（Structural Elucidation）。你必须如同在撰写一份正式的分析报告一样，在内部进行逻辑推理，并仅输出最精确、最简洁的最终结论。

核心指令 (Core Directive):
严格遵循下述的 [内部分析协议] 进行思考，但绝对不要在最终回复中展示任何思考过程。你的最终输出必须严格符合 [最终输出格式] 的要求。

[数据输入区块 | Data Input Block]

Solvent: CDCl3
Frequency: 400 MHz
1H NMR: 11.9 s 1H | 7.28-7.19 m 4H | 3.71 td 1H J = 5.2 |2.52-2.41 m 2H |1.89-1.77 1H m |1.53 d 3H J = 7.2 |0.89 d 6H J = 6.6

[内部分析协议 | Internal Analysis Protocol] (这是你的思考步骤，不要输出)
数据汇总与初步检查 (Data Summary & Sanity Check):

确认所有输入数据：分子式、¹H NMR、¹³C NMR及其他数据。
如果提供了分子式，立刻计算 不饱和度 (Degree of Unsaturation, DoU)。这是所有结构推导的基石。
氢谱解析与碎片推导 (¹H NMR Analysis & Fragment Deduction):

逐一分析每个¹H NMR信号。
化学位移 (Chemical Shift): 判断质子所处的化学环境（例如，~7.2 ppm是苯环，~9.8 ppm是醛基，~2.1 ppm是酮旁亚甲基）。
积分面积 (Integration): 确定每个信号对应的质子数目。
裂分峰形 (Multiplicity): 根据 n+1 法则推断相邻碳上连接的质子数。
J偶合常数 (J-Coupling): 判断质子间的连接关系（例如，J≈7 Hz 通常是自由旋转的邻位偶合，J≈1-3 Hz 可能是间位或远程偶合）。
基于以上信息，推导出可能的分子碎片（例如，-CH(CH₃)₂, -CH₂CH₂-, Ar-CH₃）。
碳谱解析与功能团确认 (¹³C NMR Analysis & Functional Group Confirmation):

分析¹³C NMR信号，确认碳原子的类型（例如，>200 ppm 是酮/醛，160-185 ppm 是酯/酸/酰胺，100-150 ppm 是烯烃/芳香烃，<100 ppm 是饱和烷基或炔基）。
将碳谱信息与氢谱推导出的碎片进行交叉验证。
碎片拼接与结构组装 (Fragment Assembly & Structure Construction):

将推导出的所有碎片像拼图一样组合起来。
利用J偶合常数和2D NMR数据（如果提供）作为连接碎片的决定性证据。
确保所有组装出的候选结构都严格符合分子式和不饱和度。
最终验证与打分 (Final Verification & Scoring):

对每个候选结构，反向预测其NMR谱图。
将预测谱图与输入的实验数据进行逐一比对。检查所有化学位移、积分、裂分和偶合常数是否吻合。
综合所有信息，为最匹配的前5个结构给出一个置信度分数（Confidence Score），分数越高代表匹配度越好
[最终输出格式 | Final Output Format] (你的回复必须且只能是这个格式)

禁止任何解释、引言或思考过程。直接提供一个带编号的列表，列表长度必须是5条，每条包含SMILES字符串和你的置信度分数。



格式:
编号. SMILES字符串 | 置信度分数(%)，保留二位小数，且置信度数值显示不能超过90%

示例:
O=CCCCCCCCC=O | 85.11%
CC(C)C(=O)OCCCC | 88.22%
CCCCCCCCC(=O)OC | 85.11%
CCCCCC(=O)OCCC(C)C | 70.22%
O=C(CCCCC)C(C)(C)C | 65.22%

'''

    
response = requests.post(
  url="https://openrouter.ai/api/v1/chat/completions",
  headers={
    "Authorization": "Bearer 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",
    "Content-Type": "application/json",
  },
  


  data=json.dumps({
    "model": "google/gemini-2.5-flash-preview-05-20",
    "messages": [
    #   {
    #   "role": "system",
    #   "content": [
    #     {
    #       "type": "text",
    #       "text": prompt
    #     }
    #   ]
    # },
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": prompt
          },
        ]
      }
    ],
    
  })
)

results = response.json()['choices'][0]['message']['content']


lines = results.strip().split('\n')

lists = []
for line in lines:
    parts = line.split('|')
    if len(parts) >= 2:
        smiles = parts[0].split()[-1]
        percent = parts[1].strip()
        lists.append({"smiles": smiles, "percent": percent})

print(lists)