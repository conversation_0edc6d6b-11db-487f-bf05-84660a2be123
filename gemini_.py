import requests
import json


prompt = '''
你是一位资深核磁共振光谱解析专家。根据提供的¹³C NMR数据，从候选SMILES中精确匹配出目标化合物的SMILES。输出规则：
1. **仅输出最可能的SMILES**（无需化合物名/分子式/解释）
2. 若存在不可区分的同分异构体，分行列出所有匹配的SMILES
3. 无匹配时输出：None

实验数据：
[¹³C NMR化学位移]：替换为具体数值（单位ppm）
候选SMILES（10个）：替换为具体SMILES列表
其他辅助信息（若有）：替换为补充数据
'''

SMILES = '''
    C(=O)(OCC(C)CCC)c1ccccc1
    C(=O)(CCC)OCC(C)c1ccccc1
    C(=O)(OCCC(CC)CCC)c1ccccc1C(=O)OCCC(CC)CCC
    C(=O)(OCC(CC)CCCC)c1ccccc1C(=O)OCC(CC)CCCC
    C(=O)(OCCCC)OC(C)Sc1ccccc1
    C(=O)(O)C1OC(c2ccccc2)=NC12CC2C(C)C
    C(=O)(CCC)OC1CCCOC1Sc1ccc(C)cc1
    C(=C[I+]c1ccccc1)(CCC)OS(C)(=O)=O
    C(=O)(CCC)OCCc1ccccc1
    C(=O)(OCCOCCCC)c1ccccc1
   '''


    
response = requests.post(
  url="https://openrouter.ai/api/v1/chat/completions",
  headers={
    "Authorization": "Bearer 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",
    "Content-Type": "application/json",
  },
  


  data=json.dumps({
    "model": "google/gemini-2.5-flash-preview-05-20",
    "messages": [
      {
      "role": "system",
      "content": [
        {
          "type": "text",
          "text": prompt
        }
      ]
    },
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "13C ppm: 166.50 132.70 130.62 129.53 128.27 69.87 35.79 32.53 20.05 17.03 14.30" + SMILES
          },
        ]
      }
    ],
    
  })
)

print(response.json())